#!/usr/bin/env python3
"""
Test script for incremental cache update functionality
"""

import requests
import time
import json

# API base URL
BASE_URL = "http://localhost:8000"

def test_cache_status():
    """Test cache status endpoint"""
    print("=== Testing Cache Status ===")
    try:
        response = requests.get(f"{BASE_URL}/cache/status")
        if response.status_code == 200:
            data = response.json()
            print(f"Cache loaded: {data.get('cache_loaded')}")
            print(f"Cache size: {data.get('cache_size'):,} records")
            print(f"Loaded from existing cache: {data.get('loaded_from_existing_cache')}")
            print(f"Last updated: {data.get('last_updated')}")
            print(f"Cache age: {data.get('cache_age_seconds'):.1f} seconds")
            return True
        else:
            print(f"Error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Error testing cache status: {e}")
        return False

def test_check_updates():
    """Test check for updates endpoint"""
    print("\n=== Testing Check for Updates ===")
    try:
        response = requests.get(f"{BASE_URL}/cache/check-updates")
        if response.status_code == 200:
            data = response.json()
            print(f"Has new records: {data.get('has_new_records')}")
            print(f"New record count: {data.get('new_record_count')}")
            print(f"Latest cache Entered_Time: {data.get('latest_cache_entered_time')}")
            print(f"Auto update enabled: {data.get('auto_update_enabled')}")
            print(f"Check interval: {data.get('check_interval_seconds')} seconds")
            return data
        else:
            print(f"Error: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Error checking for updates: {e}")
        return None

def test_incremental_update():
    """Test manual incremental update"""
    print("\n=== Testing Manual Incremental Update ===")
    try:
        response = requests.post(f"{BASE_URL}/cache/update-incremental")
        if response.status_code == 200:
            data = response.json()
            print(f"Update success: {data.get('success')}")
            print(f"Message: {data.get('message')}")
            
            result = data.get('result', {})
            if result.get('success'):
                print(f"New records added: {result.get('new_records_added', 0)}")
                print(f"Total records: {result.get('total_records', 0)}")
                print(f"Update time: {result.get('update_time_seconds', 0):.2f} seconds")
                print(f"Memory used: {result.get('memory_used_mb', 0):.1f} MB")
            else:
                print(f"Update reason: {result.get('reason')}")
            
            return data
        else:
            print(f"Error: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Error performing incremental update: {e}")
        return None

def test_update_config():
    """Test update configuration endpoints"""
    print("\n=== Testing Update Configuration ===")
    try:
        # Get current config
        response = requests.get(f"{BASE_URL}/cache/update-config")
        if response.status_code == 200:
            data = response.json()
            print("Current configuration:")
            print(json.dumps(data.get('config', {}), indent=2))
            print(f"Monitor running: {data.get('monitor_running')}")
            print(f"Monitor thread alive: {data.get('monitor_thread_alive')}")
            return True
        else:
            print(f"Error getting config: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Error testing update config: {e}")
        return False

def test_search_functionality():
    """Test that search still works after incremental update"""
    print("\n=== Testing Search Functionality ===")
    try:
        search_data = {
            "query": "test search query",
            "top_k": 5
        }
        
        response = requests.post(f"{BASE_URL}/search", json=search_data)
        if response.status_code == 200:
            data = response.json()
            print(f"Search success: {data.get('success')}")
            print(f"Total results: {data.get('total_results')}")
            print(f"Execution time: {data.get('execution_time_ms'):.2f} ms")
            return True
        else:
            print(f"Search error: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"Error testing search: {e}")
        return False

def main():
    """Main test function"""
    print("Starting Incremental Cache Update Tests")
    print("=" * 50)
    
    # Test 1: Check cache status
    if not test_cache_status():
        print("Cache status test failed. Is the API running?")
        return
    
    # Test 2: Check for updates
    update_info = test_check_updates()
    if update_info is None:
        print("Check updates test failed.")
        return
    
    # Test 3: Test configuration
    if not test_update_config():
        print("Update configuration test failed.")
        return
    
    # Test 4: Perform incremental update
    update_result = test_incremental_update()
    if update_result is None:
        print("Incremental update test failed.")
        return
    
    # Test 5: Verify search still works
    if not test_search_functionality():
        print("Search functionality test failed.")
        return
    
    # Test 6: Check cache status again to see changes
    print("\n=== Final Cache Status ===")
    test_cache_status()
    
    print("\n" + "=" * 50)
    print("All tests completed!")

if __name__ == "__main__":
    main()
