#!/usr/bin/env python3
"""
Test script to verify the monitor display behavior with logging
"""

import sys
import time
import threading
import logging

# Global variables for monitoring
monitor_running = False
current_monitor_status = ""

# Custom logging handler that coordinates with the monitor
class MonitorAwareStreamHandler(logging.StreamHandler):
    def __init__(self, stream=None):
        super().__init__(stream)
        
    def emit(self, record):
        try:
            # Format the message
            msg = self.format(record)
            
            # Clear monitor line, print message, then restore monitor
            if monitor_running and current_monitor_status:
                # Save cursor, move to bottom, clear line
                sys.stdout.write("\033[s")  # Save cursor
                sys.stdout.write("\033[999;1H")  # Move to bottom
                sys.stdout.write("\033[2K")  # Clear line
                sys.stdout.write("\033[u")  # Restore cursor
                
                # Print the log message normally
                sys.stdout.write(msg + '\n')
                sys.stdout.flush()
                
                # Immediately redraw the monitor status
                sys.stdout.write("\033[s")  # Save cursor
                sys.stdout.write("\033[999;1H")  # Move to bottom
                sys.stdout.write("\033[2K")  # Clear line
                sys.stdout.write(current_monitor_status)  # Write status
                sys.stdout.write("\033[u")  # Restore cursor
                sys.stdout.flush()
            else:
                # Normal logging when monitor is not running
                sys.stdout.write(msg + '\n')
                sys.stdout.flush()
                
        except Exception:
            self.handleError(record)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        MonitorAwareStreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def monitor_worker():
    """Simple monitor worker for testing"""
    global monitor_running, current_monitor_status
    
    # Terminal control sequences
    SAVE_CURSOR = "\033[s"
    RESTORE_CURSOR = "\033[u"
    MOVE_TO_BOTTOM = "\033[999;1H"
    CLEAR_LINE = "\033[2K"
    HIDE_CURSOR = "\033[?25l"
    SHOW_CURSOR = "\033[?25h"
    
    try:
        # Hide cursor
        sys.stdout.write(HIDE_CURSOR)
        sys.stdout.flush()
        
        counter = 0
        while monitor_running:
            counter += 1
            
            # Create status line
            status_line = f"[TEST MONITOR] Counter: {counter} | Time: {time.strftime('%H:%M:%S')}"
            current_monitor_status = status_line
            
            # Display monitor at bottom
            sys.stdout.write(SAVE_CURSOR)
            sys.stdout.write(MOVE_TO_BOTTOM)
            sys.stdout.write(CLEAR_LINE)
            sys.stdout.write(status_line)
            sys.stdout.write(RESTORE_CURSOR)
            sys.stdout.flush()
            
            time.sleep(2)  # Update every 2 seconds
            
    except Exception as e:
        print(f"Monitor error: {e}")
    finally:
        # Clear the status line and show cursor when stopping
        sys.stdout.write(SAVE_CURSOR)
        sys.stdout.write(MOVE_TO_BOTTOM)
        sys.stdout.write(CLEAR_LINE)
        sys.stdout.write(RESTORE_CURSOR)
        sys.stdout.write(SHOW_CURSOR)
        sys.stdout.flush()
        current_monitor_status = ""

def setup_terminal():
    """Set up terminal for monitor"""
    try:
        print("\n" * 3)  # Add space at bottom
        sys.stdout.write("\033[3A")  # Move cursor up 3 lines
        sys.stdout.flush()
        print("Test monitor will display at bottom of terminal...")
        sys.stdout.flush()
    except Exception:
        pass

def start_monitor():
    """Start the monitor"""
    global monitor_running
    
    setup_terminal()
    monitor_running = True
    monitor_thread = threading.Thread(target=monitor_worker, daemon=True)
    monitor_thread.start()
    logger.info("Test monitor started")
    return monitor_thread

def stop_monitor():
    """Stop the monitor"""
    global monitor_running
    
    monitor_running = False
    logger.info("Test monitor stopped")

def main():
    """Main test function"""
    print("="*60)
    print("Testing Monitor Display with Logging")
    print("="*60)
    
    # Start monitor
    monitor_thread = start_monitor()
    
    try:
        # Generate some log messages to test the behavior
        for i in range(20):
            time.sleep(3)
            logger.info(f"Test log message #{i+1} - This should not interfere with monitor")
            
            if i % 5 == 4:
                logger.warning(f"Warning message #{i+1} - Testing different log levels")
                
    except KeyboardInterrupt:
        print("\nStopping test...")
    finally:
        stop_monitor()
        monitor_thread.join(timeout=1)
        print("Test completed")

if __name__ == "__main__":
    main()
