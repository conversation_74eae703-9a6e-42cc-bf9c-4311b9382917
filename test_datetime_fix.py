#!/usr/bin/env python3
"""
Test script to verify datetime formatting fix
"""

from datetime import datetime

def test_datetime_formatting():
    """Test the datetime formatting logic"""
    
    # Test case 1: String with microseconds (like from database)
    test_time_str = "2011-01-05 14:42:58.903000"
    if isinstance(test_time_str, str):
        formatted_time = test_time_str.replace('T', ' ').split('.')[0]
    else:
        formatted_time = test_time_str.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"Input: {test_time_str}")
    print(f"Formatted: {formatted_time}")
    print(f"Expected: 2011-01-05 14:42:58")
    print(f"Match: {formatted_time == '2011-01-05 14:42:58'}")
    print()
    
    # Test case 2: String with T separator
    test_time_str2 = "2011-01-05T14:42:58.903000"
    if isinstance(test_time_str2, str):
        formatted_time2 = test_time_str2.replace('T', ' ').split('.')[0]
    else:
        formatted_time2 = test_time_str2.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"Input: {test_time_str2}")
    print(f"Formatted: {formatted_time2}")
    print(f"Expected: 2011-01-05 14:42:58")
    print(f"Match: {formatted_time2 == '2011-01-05 14:42:58'}")
    print()
    
    # Test case 3: Datetime object
    test_datetime = datetime(2011, 1, 5, 14, 42, 58, 903000)
    if isinstance(test_datetime, str):
        formatted_time3 = test_datetime.replace('T', ' ').split('.')[0]
    else:
        formatted_time3 = test_datetime.strftime('%Y-%m-%d %H:%M:%S')
    
    print(f"Input: {test_datetime}")
    print(f"Formatted: {formatted_time3}")
    print(f"Expected: 2011-01-05 14:42:58")
    print(f"Match: {formatted_time3 == '2011-01-05 14:42:58'}")
    print()
    
    # Test SQL query format
    sql_query = f"""
    SELECT COUNT(*) as new_count
    FROM ReportNLP
    WHERE empty_report = 0
      AND empty_embedding = 0
      AND extracted_text IS NOT NULL
      AND embedding_vector IS NOT NULL
      AND Entered_Time > '{formatted_time}'
    """
    
    print("Generated SQL Query:")
    print(sql_query)

if __name__ == "__main__":
    test_datetime_formatting()
